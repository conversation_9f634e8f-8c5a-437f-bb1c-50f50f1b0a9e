<template>
  <van-popup v-model:show="limitPopup" position="center" @click-overlay="closeOnClickOverlay">
    <div class="fill-bk-all">
      <div class="fill-bk">
        <div class="titleDivAll">
          <img v-if="fileType === 1" src="//img10.360buyimg.com/imgzone/jfs/t1/303255/33/3385/8246/681af7d4F9b1b5ccb/e94098d56a77de33.png" alt="" />
          <img v-else src="//img10.360buyimg.com/imgzone/jfs/t1/300955/36/3345/14451/681af855F4b6b1bf9/15820bd2ba9fd12e.png" alt="" />
          <div class="backDiv" @click="close">返回</div>
        </div>
        <div class="promptDiv" v-if="fileType === 1">
          *注意：本页面填写的家长姓名，手机号码，收货地址将作为后续赠品奶粉的收货信息。家长姓名需要与出生证明信息一致。手机号码必须与京东注册手机号保持一致。
        </div>
        <div class="infoDivAll">
          <van-cell-group inset>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" v-model.trim="parentName" class="field" name="家长姓名" maxlength="10" label="家长姓名：" placeholder="家长姓名" />
              <div v-if="fileType === 1" class="textDiv">*父母任一姓名即可，且需要与出生证明信息保持一致</div>
            </div>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" v-model.trim="babyName" class="field" name="宝宝姓名" maxlength="10" label="宝宝姓名：" placeholder="宝宝姓名" />
              <div v-if="fileType === 1" class="textDiv">*需要与出生证明信息内容保持一致</div>
            </div>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" v-model="babyBir" class="field" name="宝宝生日" label="宝宝生日：" readonly placeholder="点击选择宝宝生日" @click="changeDate" />
              <div v-if="fileType === 1" class="textDiv">*宝宝月龄需在24个月之内，且需要与出生证明信息保持一致</div>
            </div>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" v-model.trim="phone" class="field" name="手机号" label="手机号：" placeholder="手机号" maxlength="11" />
              <div v-if="fileType === 1" class="textDiv">*需填写京东注册手机号</div>
            </div>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" v-model="addressCode" is-link readonly class="field" name="地址" label="收货地址：" placeholder="点击选择收货地址" @click="changeArea()" />
              <div v-if="fileType === 1" class="textDiv">*需填写未在本店购买商品预留过的全新地址</div>
            </div>

            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" v-model.trim="address" class="field" name="详细地址" label="详细地址：" placeholder="请输入详细地址"/>
              <div v-if="fileType === 1" class="textDiv">*需填写未在本店购买商品预留过的全新地址</div>
            </div>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <van-field :disabled="fileType === 2" maxlength="10" v-model.trim="bornNo" class="field" name="出生证明编号" label="出生证明编号：" placeholder="请输入出生证明编号"/>
            </div>
            <div class="fieldItemDiv" :style="fileType !== 1 ? {'marginBottom': '0.15rem'} : ''">
              <div class="field uploadDiv">
                <div v-if="fileType === 1">上传出生证明：</div>
                <div v-if="fileType === 1" style="fontSize:0.18rem; color: #ff4943">请上传一张出生证明图片，一张手持出生<br> 证明图片，一段手持出生证明拍照的视频</div>
                <div v-else-if="fileType === 2">出生证明：</div>
                <div v-if="fileType === 1" class="uploadImgDiv" @click="uploadImgClick()">上传</div>
                <div v-else class="uploadImgDiv1" @click="uploadImgClick()">上传</div>
              </div>
            </div>
            <div class="textDiv1" v-if="fileType === 1">*以上信息均需与出生证明相同，图片必须清晰</div>
            <div class="textDiv2" v-if="fileType === 1">提示：由于我们的活动是针对全渠道品牌新客，我们需要用您的手机号和出生证明判断您是否为我品全渠道新客，同时由于我们的产品为婴幼儿奶粉，我们需要了解宝宝的年龄段以判断宝宝是否属于我们产品的使用群体。如果您不希望提供以上信息，您可以拒绝参加我们的新客活动，我们将对以上信息采取严格的保密措施，以保护您和宝宝的个人信息权利。</div>
            <div class="checkBoxDiv" v-if="fileType === 1">
              <van-checkbox v-model="privacyPolicyCheckbox" checked-color="#000" icon-size="12px"></van-checkbox>
              <p>我已阅读并知晓<span class="underline" @click="showPrivacyPolicy = true">《伊利公司隐私政策》</span></p>
            </div>
          </van-cell-group>
        </div>
        <div class="btnDiv" v-if="fileType === 1">
          <div class="submitDiv" @click="submitClick()">确认提交</div>
        </div>
      </div>
    </div>
  </van-popup>
  <div>
    <!--  时间选择-->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker v-model="pickerDate" @confirm="confirmBirthday" @cancel="showDatePicker = false" :max-date="maxDate" :min-date="minDate" />
    </van-popup>
    <!--  地址选择-->
    <van-popup v-model:show="showArea" position="bottom">
      <van-area :area-list="areaList" @confirm="confirmArea" @cancel="showArea = false" />
    </van-popup>
    <!--上传图片-->
<!--    @click-overlay="uploadCloseOnClickOverlay"-->
    <van-popup v-model:show="showUpLoadImg" position="center">
      <UploadImgPop :fileType="fileType" :babyImg="babyImg" :parentsImg="parentsImg" :video="video" :isCanSubImg="isCanSubImg" :isCanSubParentsImg="isCanSubParentsImg" :isCanSubVideo="isCanSubVideo" @saveImg="saveImg" @close="showUpLoadImg = false" />
    </van-popup>
    <van-popup v-model:show="showReviewTip" position="center">
      <ReviewTipPop :popType="popType" :errMessage="errMessage" @sureSubmitInfo="sureSubmitInfo()" @close="showReviewTip=false" />
    </van-popup>
    <van-popup v-model:show="showPrivacyPolicy" position="center">
      <PrivacyPolicy @close="showPrivacyPolicy=false" />
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch, computed } from 'vue';
import dayjs from 'dayjs';
import { areaList } from '@vant/area-data';
import UploadImgPop from './UploadImg.vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import ReviewTipPop from './reviewTip.vue';
import { httpRequest } from '@/utils/service';
import PrivacyPolicy from '../components/PrivacyPolicy.vue';

const privacyPolicyCheckbox = ref(true);
const showPrivacyPolicy = ref(false);
const popType = ref(1);
const errMessage = ref('');
const props = defineProps(['fileType', 'show', 'uploadImg', 'isCanSubImg', 'isCanSubParentsImg', 'isCanSubVideo']);
const emits = defineEmits(['update:show', 'close', 'submitInfo']);
const limitPopup = ref(props.show);
const showUpLoadImg = ref(false);
const maxDate = new Date();
const minDate = new Date(new Date().getFullYear() - 100, new Date().getMonth(), new Date().getDate());
const parentName = ref('');
const babyName = ref('');
const babyBir = ref('');
const phone = ref('');
const province = ref('');
const city = ref('');
const county = ref('');
const address = ref('');
const bornNo = ref('');
const babyImg = ref('');
const parentsImg = ref('');
const video = ref('');
const showReviewTip = ref(false); // 审核进度弹窗

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);
const close = () => {
  emits('close');
};
const pickerDate = ref([dayjs().format('YYYY'), dayjs().format('MM'), dayjs().format('DD')]);
const showDatePicker = ref(false);
const changeDate = () => {
  showDatePicker.value = true;
};
const confirmBirthday = (res: any) => {
  pickerDate.value = res.selectedValues;
  babyBir.value = res.selectedValues.join('/');
  showDatePicker.value = false;
};
// 住址修改
const showArea = ref(false);
const changeArea = () => {
  if (props.fileType === 2) {
    return;
  }
  showArea.value = true;
};
const confirmArea = ({ selectedOptions }: any) => {
  // console.log(selectedOptions, 'selectedOptions====');
  showArea.value = false;
  province.value = selectedOptions[0].text;
  city.value = selectedOptions[1].text;
  county.value = selectedOptions[2].text;
};
const addressCode = computed(() => {
  if (province.value && city.value && county.value) {
    return `${province.value}/${city.value}/${county.value}`;
  }
  return '';
});
// 上传图片
const uploadImgClick = () => {
  console.log('上传图片');
  showUpLoadImg.value = true;
};
const saveImg = (data) => {
  const { babyImg1, parentsImg1, videoUrl1 } = data;
  babyImg.value = babyImg1;
  parentsImg.value = parentsImg1;
  video.value = videoUrl1;
  showUpLoadImg.value = false;
};
// 家长姓名校验
const validatorNameMessage = (val: string) => {
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!val) {
    return '请输入家长姓名';
  }
  if (reg.test(val)) {
    return '家长姓名不能包含表情';
  }
  return '';
};
// 宝宝姓名校验
const validatorBabyNameMessage = (val: string) => {
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!val) {
    return '请输入宝宝姓名';
  }
  if (reg.test(val)) {
    return '宝宝姓名不能包含表情';
  }
  return '';
};
const validatorPhoneMessage = (val: string) => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!val) {
    return '请输入手机号';
  }
  if (!checkPhone.test(val)) {
    return '请输入正确的手机号';
  }
  return '';
};

// 确认提交档案信息
const submitClick = () => {

  if (validatorNameMessage(parentName.value)) {
    showToast(validatorNameMessage(parentName.value));
    return;
  }
  if (validatorBabyNameMessage(babyName.value)) {
    showToast(validatorBabyNameMessage(babyName.value));
    return;
  }
  if (!babyBir.value) {
    showToast('请输入宝宝生日');
    return;
  }

  if (validatorPhoneMessage(phone.value)) {
    showToast(validatorPhoneMessage(phone.value));
    return;
  }
  if (!province.value) {
    showToast('请选择收货地址');
    return;
  }
  if (!address.value) {
    showToast('请输入详细地址');
    return;
  }
  if (!bornNo.value) {
    showToast('请输入出生证明编号');
    return;
  }
  if (props.uploadImg) {
    if (props.isCanSubImg) {
      if (!babyImg.value) {
        showToast('请上传宝宝出生证明');
        return;
      }
    }
    if (props.isCanSubParentsImg) {
      if (!parentsImg.value) {
        showToast('请上传父母手持出生证明照片');
        return;
      }
    }
    if (props.isCanSubVideo) {
      if (!video.value) {
        showToast('请上传出生证明视频');
        return;
      }
    }
  }
  if (privacyPolicyCheckbox.value) {
    popType.value = 1;
    // errMessage.value = '请您先统一并阅读伊利隐私协议';
    showReviewTip.value = true;
  } else {
    showToast('请您先统一并阅读伊利隐私协议');
  }
};
// 确认提交档案信息
const sureSubmitInfo = async () => {
  const submitData = {
    parentName: parentName.value,
    babyName: babyName.value,
    babyBir: babyBir.value,
    phone: phone.value,
    province: province.value,
    city: city.value,
    county: county.value,
    address: address.value,
    bornNo: bornNo.value,
    upload: {
      imgOne: babyImg.value,
      imgTwo: parentsImg.value,
      video: video.value,
    }
  }
  console.log(submitData, '确认一致');
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/91010/submitInfo', submitData);
    console.log(data, '提交信息');
    showReviewTip.value = false;
    emits('submitInfo', submitData);
    closeToast();
  } catch (e) {
    // showToast(e.message);
    if (e.message === '很抱歉您不符合新客身份无法参与') {
      popType.value = 5;
      showReviewTip.value = true;
    } else {
      popType.value = 2;
      errMessage.value = e.message ? e.message : '很抱歉您不符合老客身份  无法参与';
      showReviewTip.value = true;
    }
    closeToast();
  }
};
const closeOnClickOverlay = () => {
  // console.log('关闭=====');
  limitPopup.value = false;
  emits('update:show', false);
  emits('close');
};
const init = async () => {
  if (props.fileType !== 1) {
    try {
      const { data } = await httpRequest.post('/91010/submitInfoSearch');
      console.log(data, '已填写的信息');
      parentName.value = data.parentName;
      babyName.value = data.babyName;
      babyBir.value = data.babyBir;
      phone.value = data.phone;
      province.value = data.province;
      city.value = data.city;
      county.value = data.county;
      address.value = data.address;
      bornNo.value = data.bornNo;
      babyImg.value= (data.upload && data.upload.imgOne) ? data.upload.imgOne : '';
      parentsImg.value= (data.upload && data.upload.imgTwo) ? data.upload.imgTwo : '';
      video.value= (data.upload && data.upload.video) ? data.upload.video : '';
      closeToast();
    } catch (e) {
      showToast(e.message);
    }
  }
}
init();
</script>

<style lang="scss">
.fill-bk-all {
  .fill-bk{
    //background: url(//img10.360buyimg.com/imgzone/jfs/t1/294600/25/2667/30188/6819dbe4Feba27fd3/897074c9a5183e16.png) no-repeat;
    //background-size: 100% 100%;
    //width:5.91rem;
    //height: 7.0rem;
    //padding-top: 1.3rem;
    position: relative;
    width: 7.5rem;
    height: 100vh;
    background-color: #fff;
  }
  .titleDivAll{
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/291596/15/3073/11284/6819b3bdFcf5fa09a/4f6c3b61397769ae.png');
    background-size: 100%;
    background-repeat: no-repeat;
    width: 7.5rem;
    position: relative;
    img{
      width:100%;
    }
    .backDiv{
      position: absolute;
      top: 0.25rem;
      left: 0.24rem;
      font-size: 0.24rem;
      color: #7b5e35;
      border: .01rem solid #7b5e35;
      border-radius: 0.3rem;
      padding: 0.03rem 0.15rem;
    }
  }
  .promptDiv{
    flex-wrap: nowrap;
    margin-bottom: .25rem;
    display: flex;
    padding: 0.2rem 0.14rem 0 0.14rem;
    font-size: 0.2rem;
    color: #7b5e35;
    font-family: FZLanTingHei-R-GBK;
  }
  .infoDivAll{
    //max-height: 6.63rem;
    //overflow-y: scroll;
    padding: 0 0.14rem;
    .van-cell-group {
      background: #fff;
      width: 100%;
      margin: auto;
    }
    .van-cell {
      background: #fff;
    }
    .van-form {
      min-height: 3rem;
      max-height: 6rem;
      overflow: hidden;
      overflow-y: scroll;
    }
    .fieldItemDiv{
      //margin-bottom: 0.35rem;
      margin-top: 0.2rem;
      .textDiv{
        color: #7b5e35;
        font-size: 0.18rem;
        margin-top: 0.1rem;
        //margin: 0.13rem 0 0.2rem 0;
      }
      .textDivLast {
        color: #7b5e35;
        font-size: 0.18rem;
        margin: 0.13rem 0 0.1rem 0;
      }
      .van-field__label{
        color: #e90405;
        font-size: 0.22rem;
        margin-right: auto;
        width: auto;
        font-weight: bold;
      }
      .field {
        //width: 6rem;
        border: 0.02rem solid #f1d67e;
        font-size: 0.22rem;
        color: #7b5e35;
        border-radius: 0.2rem;
        --van-field-label-width: 1rem;
        min-height: 0.6rem;
        display: flex;
        align-items: center;
        padding-top: 0;
        padding-bottom: 0;
      }
      .van-field__control{
        color: #7b5e35;
      }
      .uploadDiv{
        color: #e90405;
        padding: 0 0 0 14px;
        font-size: 0.22rem;
        position: relative;
        font-weight: bold;
      }
      .uploadImgDiv{
        width: 1.27rem;
        height: 0.50rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/295741/3/3066/4773/681adefbF45c2ca5d/f6f130a7f814ce72.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        font-size: 0;
        position: absolute;
        right: 0.1rem;
      }
      .uploadImgDiv1{
        width: 1.27rem;
        height: 0.50rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/302627/39/3449/4988/681adefaFb637990d/fdca09c61ca3a10e.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        font-size: 0;
        position: absolute;
        right: 0.1rem;
      }
    }
    .textDiv1{
      padding-left: .15rem;
      margin-bottom: .13rem;
      font-size: .18rem;
      color: #7b5e35;
      margin-top:0.13rem;
    }
    .textDiv2{
      padding-left: .15rem;
      margin-bottom: .13rem;
      font-size: .18rem;
      color: #7b5e35;
    }
    .checkBoxDiv{
      display: flex;
      font-size: .2rem;
      color: #323233;
      p{
        margin-left: 0.1rem;
      }
      span{
        color: #008cff;
      }
    }
  }
  .btnDiv{
    //position: absolute;
    //bottom: 0.6rem;
    margin-left: calc(50% - 2.73rem / 2);
    margin-top: 0.25rem;
    .submitDiv{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/291444/6/2175/10821/681adf02F7ee9e520/c9f364b8212485a9.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 2.73rem;
      height: 0.75rem;
      font-size: 0;
      //margin-left: calc(50% - 2.69rem / 2);
    }
  }
}
</style>
