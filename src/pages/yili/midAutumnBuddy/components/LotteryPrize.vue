<template>
  <div class="com-bg">
    <div class="progress-container">
      <div class="progress-fill" :style="{ width: activityInfo.freeOrderSendProgress + '%' }"></div>
      <svg class="progress-text" viewBox="0 0 100 30" xmlns="http://www.w3.org/2000/svg">
        <text x="50" y="16" text-anchor="middle" dominant-baseline="middle" fill="#fff" stroke="#c9341b" stroke-width="6" stroke-linejoin="round" font-size="16" font-weight="bold" paint-order="stroke">{{ activityInfo.freeOrderSendProgress }}%</text>
      </svg>
    </div>
    <div class="prize-content">
      <div class="prize-list">
        <div class="prize-item">
          <img src="https://img10.360buyimg.com/imgzone/jfs/t1/334314/25/9236/101051/68b94afdFd63b93d8/6ba666f2596dd80f.png" alt="" class="prize-img" />
        </div>
        <div class="prize-item">
          <img src="https://img10.360buyimg.com/imgzone/jfs/t1/334732/38/9202/83492/68b94afdFa1c348fc/1e62c6e458964d49.png" alt="" class="prize-img" />
        </div>
      </div>
      <div class="prize-list-swiper swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide prize-item" v-for="(item, index) in prizeList" :key="index">
            <img :src="item" alt="" class="prize-img" />
          </div>
        </div>
      </div>
    </div>
    <div class="pre-btn" @click="toPrev"></div>
    <div class="next-btn" @click="toNext"></div>
    <div class="lottery-btn">
      <div class="lottery-btn-text" @click="showLotteryRecord">立即抽奖（{{ activityInfo.userRemainChance }}）</div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/333353/28/7685/9764/68b64f5fF911f596a/a41ed9b887629832.png" alt="" class="select-prize-btn" @click="showPrizeListPop" />
  </div>
  <VanPopup v-model:show="lotteryRecordPop" teleport="body" :close-on-click-overlay="false">
    <LotteryRecord v-if="lotteryRecordPop" @close="lotteryRecordPop = false" @toLottery="toLottery"></LotteryRecord>
  </VanPopup>
  <VanPopup v-model:show="lotteryResultPop" teleport="body" :close-on-click-overlay="false">
    <LotteryResult :prize-info="lotteryResult" @close="lotteryResultPop = false" @showShareCard="showShareCard"></LotteryResult>
  </VanPopup>
  <VanPopup v-model:show="prizeListPop" teleport="body" :close-on-click-overlay="false">
    <div class="prize-list-pop">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/328145/8/15993/1398079/68b93be4Fda05bef4/970831ed7d47521d.png" alt="" class="prize-list-pop-img" />
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="prize-list-pop-close" @click="prizeListPop = false" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { nextTick } from 'process';
import { ref, onMounted } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import LotteryRecord from '../pop/LotteryRecord.vue';
import LotteryResult from '../pop/LotteryResult.vue';
import { activityInfo, checkActStatus, getActivityInfo, getAllPrizeRecord } from '../hooks';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

Swiper.use([Autoplay]);

const lotteryRecordPop = ref(false);
const lotteryResultPop = ref(false);
const prizeListPop = ref(false);

const prizeList = ref([
  '//img10.360buyimg.com/imgzone/jfs/t1/333566/30/9102/16006/68b92ebbFb6ec0d91/bbb56a1cad5049ab.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/338263/32/6571/16070/68b92ebaF9fe8d09e/3d4ad0df31ef4f14.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/328050/5/15908/17178/68b92ebaFeb5ab49d/08ca951bd0081bec.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/327308/12/15892/17056/68b92eb9Fe1be49d4/fb35961d4ec01b4f.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/336915/12/6658/69641/68b92eb8Fb4cf2d57/215be65d2a0f3857.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/331167/35/9167/81411/68b92eb8F7e32270c/17ef49f104211db4.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/332098/19/9003/84191/68b92eb7F72d8ad81/3d738739095a5c33.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/332593/3/9087/57570/68b92eb6F1c919270/90ddf55955086d15.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/325269/37/15807/206448/68b92eb5Fddd850f1/28db615c2bb38384.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/337717/23/6594/58758/68b92eb4Fd4a64a9f/f747f720790d44b4.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/330737/1/9112/56422/68b92eb4F779a1186/9ce5672ebd3f00fc.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/332194/13/8926/62950/68b92eb3Ffac9b012/23f98ff5bfdfbfee.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/324392/2/16059/55309/68b92eb2Fbc606e8a/47fa51d6d46c11fa.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/325646/15/15817/61929/68b92eb2F3bafa385/28499ad2faf3ac35.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/340450/35/6595/75041/68b92eb1Fab11cc42/af9f57e11b5d15b4.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/333313/5/9018/62588/68b92eb0F055326fc/82929f0346270f3b.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/334849/29/9126/68850/68b92eafF6f1285ec/eefa077b11841527.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/329105/23/9243/69732/68b92eafF87a79980/56a6f603d6217f8a.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/332983/3/9175/69661/68b92eaeF5b5f4c7b/b7c8795d75c40608.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/325145/14/15506/68698/68b92eadF8af22004/0d36cac7dcb2d2fe.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/336193/2/6635/79358/68b92eacF10aaa734/6c58872810d1b7cd.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/327372/13/15334/55775/68b92eacF43270509/3439ae91365a512d.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/329507/34/9128/91816/68b92eabF57f5cfdc/4eeba4a536d2cbfe.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/293979/40/24651/51466/68b92eaaF34fa63cb/7c66d8ec6e9bf5b1.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/334489/11/9030/62641/68b92eaaF299e10bf/44c2ab85b0dd533f.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/295136/40/27718/75797/68b92ea9F4315d96c/f2d9b8604dc6f025.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/339596/17/7134/82902/68ba4e78Fee03ed37/1584ad938abc34e6.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/324014/8/16353/124250/68ba4e77F40b2dff4/41010fd3e729d14c.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/324695/24/16344/122424/68ba4e77F494b5abd/c6ceb5e293aa0028.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/327883/27/15269/112935/68ba4e77F114d681f/01e49af35a92e32d.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/331301/6/9645/132563/68ba4e76Fa6fa5de6/265b22d8aec569d2.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/337339/3/7108/98534/68ba4e76F53721241/0b8e3517392a7f2d.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/336471/34/7091/128921/68ba4e76Fb4b013ca/e793107309d9a272.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/325997/24/16338/98534/68ba4e76Feac0c2b3/5851a5e0a242ad0c.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/339317/2/6909/149962/68ba4e75F388e3130/2f9798f2bc15e236.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/338808/28/7143/160267/68ba4e75Fde77d7a2/d4b3f92f44f0c32c.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/339551/35/7172/161073/68ba4e75F0f2aa4de/ffe5111f7692cb89.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/337790/13/6532/118335/68ba4e75Fe03e4316/4b405e7e97598440.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/327424/1/15762/101191/68ba4e74Fb6192213/25aff211206a4580.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/328359/27/16603/99615/68ba4e74Fbd5d3fc7/41745b40146fd627.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/323925/2/16238/118098/68ba4e74F6a410968/3f8257b4d255cd31.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/332211/23/9604/109661/68ba4e74F723c6397/62fa8833c092371d.png',
]);

const showPrizeListPop = () => {
  checkActStatus();
  prizeListPop.value = true;
};

const showLotteryRecord = () => {
  checkActStatus();
  if (activityInfo.userRemainChance <= 0) {
    showToast('暂无抽奖机会');
    return;
  }
  lotteryRecordPop.value = true;
};
const lotteryResult = ref<any>({
  prizeType: 0,
});
const toLottery = async (chanceId: string) => {
  lotteryRecordPop.value = false;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/lotteryDraw', {
      chanceId,
    });
    if (data.status === 0) {
      data.prizeType = 0;
    }
    lotteryResult.value = data;
    lotteryRecordPop.value = false;
    lotteryResultPop.value = true;
    closeToast();
    getActivityInfo();
    getAllPrizeRecord();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

let prizeSwiper: Swiper;
const toPrev = () => {
  if (prizeSwiper) {
    prizeSwiper.slidePrev();
  }
};
const toNext = () => {
  if (prizeSwiper) {
    prizeSwiper.slideNext();
  }
};
onMounted(() => {
  nextTick(() => {
    prizeSwiper = new Swiper('.prize-list-swiper', {
      slidesPerView: 2,
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
      },
      loop: true,
    });
  });
  getAllPrizeRecord();
});

const emits = defineEmits(['showShareCard']);
const showShareCard = (id: string) => {
  lotteryResultPop.value = false;
  emits('showShareCard', id);
};
</script>

<style scoped lang="scss">
.com-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/326951/33/10426/215568/68abc54dF2932c6ca/a408991573a52672.png') no-repeat;
  background-size: 100%;
  height: 7.65rem;
  position: relative;
  padding-top: 0.9rem;
  margin-bottom: -1.8rem;
}

.progress-container {
  position: relative;
  width: 6.68rem;
  height: 0.325rem;
  margin: 0 auto 0.65rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/325091/23/10682/1348/68abc54fFbb791e3a/2c4d04def1e6d82f.png') no-repeat;
  background-size: 100%;
  padding: 0.047rem;

  .progress-fill {
    height: 0.23rem;
    border-radius: 0.115rem;
    background: url('https://img10.360buyimg.com/imgzone/jfs/t1/329448/16/3711/8242/68abd062F9fb5ef7c/f8ef859616e3ec5c.png') no-repeat;
    background-size: 6.61rem 100%;
    background-position-x: right;
  }
  .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.5rem;
    height: 0.325rem;
  }
}
.prize-content {
  width: 6.28rem;
  margin: 0 auto;
  display: flex;
}
.prize-list {
  width: 3.14rem;
  overflow: hidden;
  display: flex;
  justify-content: space-evenly;
  .prize-item {
    width: 1.57rem;
    height: 1.65rem;
    margin: 0 auto;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    .prize-img {
      // width: 1.47rem;
      height: 1.6rem;
    }
  }
}
.prize-list-swiper {
  width: 3.14rem;
  overflow: hidden;
  .prize-item {
    width: 1.57rem;
    height: 1.65rem;
    margin: 0 auto;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    .prize-img {
      // width: 1.47rem;
      height: 1.6rem;
    }
  }
}
.pre-btn {
  position: absolute;
  top: 2.507rem;
  left: 0.227rem;
  width: 0.4rem;
  height: 0.407rem;
}
.next-btn {
  position: absolute;
  top: 2.507rem;
  right: 0.227rem;
  width: 0.4rem;
  height: 0.407rem;
}
.lottery-btn {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/338273/18/1292/111875/68abdc15F6772101b/5ea8db9886dfdb83.png');
  background-size: 100%;
  width: 5.56rem;
  height: 1.333rem;
  margin-top: 0.25rem;
  margin-left: 1.05rem;
  padding-top: 0.26rem;
  padding-left: 0.2rem;
  .lottery-btn-text {
    text-align: center;
    font-size: 0.43rem;
    line-height: 0.43rem;
    font-weight: bold;
    background-image: linear-gradient(0deg, #ffd077, #fefdfb);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
}
.select-prize-btn {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 4.8rem;
  width: 2.8rem;
}
.prize-list-pop {
  width: 6.4rem;
  height: 70vh;
  overflow-y: auto;
  .prize-list-pop-img {
    width: 6.4rem;
  }
}
.prize-list-pop-close {
  margin: 0.2rem auto 0;
  width: 0.75rem;
}
</style>
