import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // thresholdPopup: JudgmentConditions,
  // backActRefresh: true,
  urlPattern: '/custom/:activityType/:templateCode',
};

const _decoData = {
  pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/300613/6/23010/143085/6877093cFeea8aedf/5747354212930570.png',
  actBg: '',
  actBgColor: '#fff7db',
  ruleBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/290354/18/21747/3807/687709f6F20e6ea84/84a0fb9b6b396c13.png',
  btnTextColor: '#fff',
  step1Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/306754/5/16262/172391/687709b0F16956cea/4fbacf4ab801924d.png',
  step2Bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/312599/32/17578/636207/6877094cF20f95bc5/a753b614582625cd.png',
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/291271/31/17856/410291/687709dfFbc8b3407/5b70f738f50b6bda.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/292373/31/21895/34183/68771450F07365108/73e61b9e33dfb08c.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/299499/40/18643/8500/68771450Fb10afd00/37925ad80b74224b.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/304568/20/18468/46279/68771450F84be09c6/5679827e9f921fcf.jpg'
};

init(config).then(({ baseInfo, pathParams, decoData, userInfo }) => {
  // 设置页面title
  console.log(baseInfo, 'baseInfo基础信息');
  document.title = baseInfo?.activityName || '伊利抽盲盒';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.provide('userInfo', userInfo);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
