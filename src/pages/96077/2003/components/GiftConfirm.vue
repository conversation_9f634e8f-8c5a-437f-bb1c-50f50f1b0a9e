<template>
  <div class="box">
    <div class='dialog'>
      <div class="title">- 权益仅可领取{{multiplePrizeCanReceiveNum}}次 -</div>
      <div class="subtitle">{{multiplePrizeNum}}大权益任选其{{multiplePrizeCanReceiveNum}}领取哦</div>
      <div class="dialog_rule">
        <img class="prizeImg" :src="giftInfo.prizeImg" alt="">
      </div>
      <div class="confirm" @click="confirmSub"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  prizeId: string;
  status: number,
  remainCount: number,
  sendTotalCount: number,
  area: number,
}
const props = defineProps({
  giftInfo: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
  multiplePrizeNum: {
    type: Number,
    default: 0,
  },
  multiplePrizeCanReceiveNum: {
    type: Number,
    default: 0,
  },
});
console.log('giftInfo', props.giftInfo);
const emits = defineEmits(['close', 'drawSuccess']);
let isSubmitting = false;
const confirmSub = async () => {
  console.log(props.giftInfo.prizeId);
  if (isSubmitting) {
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  isSubmitting = true;
  try {
    const result = await httpRequest.post('/96077/receivePrize', { prizeId: props.giftInfo.prizeId });
    console.log('result', result);
    if (!result.data) {
      return;
    }
    showToast('领取成功~');
    emits('drawSuccess', result);
  } catch (e) {
    closeToast();
    showToast('您与大奖擦肩而过~');
    emits('drawSuccess');
    console.log(e);
  }
};
</script>

<style scoped lang="scss">
.box {

  .dialog {
    width: 6.9rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/227345/13/27509/102377/66da7777F85c25ba6/d10d7399c5fa1b61.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.1rem 0.85rem;
    .title {
      font-size: 0.42rem;
      text-align: center;
      font-weight: bold;
      letter-spacing: 0.08rem;
      color: #1b3f7d;
      margin: 0.3rem 0 0.1rem;
    }
    .subtitle {
      font-size: 0.28rem;
      font-weight: bold;
      letter-spacing: 0.07rem;
      text-align: center;
      color: #1b3f7d;
    }
    .dialog_rule {
      max-height: 3.73rem;
      overflow-y: auto;
      font-size: 0.21rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #1b3f7d;
      margin-top: 0.1rem;
      text-align: left;
    }
    .prizeImg {
      width:2rem;
      height:2rem;
      margin: 0.1rem auto;
    }
    .confirm {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/232777/14/24981/9192/66de5f6cFd88ab96e/404146b7df73b396.png) no-repeat;
      background-size: 100% 100%;
      width: 2.16rem;
      height: 0.66rem;
      margin: 0 auto;
    }
  }

}
</style>
