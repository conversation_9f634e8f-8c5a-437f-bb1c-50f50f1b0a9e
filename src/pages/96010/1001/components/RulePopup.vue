<template>
  <div class="rule-bk">
    <div class="close" @click="close"></div>
    <div class="content" v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 100vw;
  height: 9.18rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/328411/6/17632/45408/68bf9661Fcbab8203/ecf80bf7b9bfaa49.png);
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 1rem 0 0;

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.3rem;
    width: 0.6rem;
    height: 0.6rem;
    /* background-color: aliceblue;*/
  }

  .content {
    height: 8.13rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #000;
    white-space: pre-wrap;
    padding: 0.2rem 0.5rem 0;
  }
}
</style>
