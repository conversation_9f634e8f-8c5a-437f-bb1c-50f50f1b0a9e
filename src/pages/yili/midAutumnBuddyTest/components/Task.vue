<template>
  <div class="com-bg">
    <div class="task-num">({{ taskNum }}/2)</div>
    <div class="join-btn">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/338203/16/1324/10356/68abc550F0d89f0fd/f9c25fe2738e7776.png" alt="" v-if="groupOneFinishTask && groupTwoFinishTask" />
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/330747/4/3856/9058/68abc550F70cb3ad7/f89293da70823682.png" alt="" v-else @click="toOpenCard" />
    </div>
    <div class="add-cart-btn">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/333913/2/3771/10432/68abc550F431d80c8/332901d4924cff20.png" alt="" v-if="activityInfo.addCartFinish" />
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/324428/31/10619/9214/68abc54fF7e2ff85a/f39af62f7f310dd9.png" alt="" v-else @click="showAddCartPop" />
    </div>
  </div>

  <VanPopup v-model:show="addShoppingCartPop" teleport="body" :close-on-click-overlay="false">
    <AddShoppingCart @close="addShoppingCartPop = false"></AddShoppingCart>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { activityInfo, checkActStatus, groupOneFinishTask, groupTwoFinishTask, taskNum } from '../hooks';
import AddShoppingCart from '../pop/AddShoppingCart.vue';

const addShoppingCartPop = ref(false);

const showAddCartPop = () => {
  checkActStatus();
  addShoppingCartPop.value = true;
};

const toOpenCard = () => {
  checkActStatus();
  const returnUrl = encodeURIComponent(`${window.location.href}`);
  if (!groupOneFinishTask.value) {
    window.location.href = `https://crmsam.jd.com/union/index.html?token=F068F8514165010404AD6E1025F4CC4A&url=${returnUrl}`;
  } else if (!groupTwoFinishTask.value) {
    window.location.href = `https://crmsam.jd.com/union/index.html?token=F56607EF370FBC91FA2E95A70E3E7088&url=${returnUrl}`;
  }
};
</script>

<style scoped lang="scss">
.com-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/331788/2/9166/203848/68b93a87Fd7e4cb8d/c3919cb8c17dd2aa.png') no-repeat;
  background-size: 100%;
  height: 9.4rem;
  position: relative;
  padding-top: 1.92rem;
  margin-bottom: 0.13rem;
  font-weight: bold;
  .task-num {
    position: absolute;
    top: 1.73rem;
    left: 2.2rem;
    font-size: 0.24rem;
    line-height: 0.24rem;
    color: #cb2e19;
  }
  .join-btn {
    width: 1.4rem;
    height: 0.607rem;
    position: absolute;
    top: 1.92rem;
    right: 0.34rem;
    img {
      width: 100%;
    }
  }
  .add-cart-btn {
    width: 1.4rem;
    height: 0.607rem;
    position: absolute;
    top: 7.4rem;
    right: 0.34rem;
    img {
      width: 100%;
    }
  }
}
</style>
