<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-content" :class="{ 'create-img': isCreateImg }">
      <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRule = true">活动规则</div>
      <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showMyPrize = true">我的奖品</div>
      <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="ShowToast">进店逛逛</div>
    </div>
    <div class="prizeBox">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/336848/5/7010/385/68bea3b9F36faf59e/aa1c9da47edd914e.png" alt="" class="leftBtn"/>
      <div class="prizeListClass">
        <div class="prizeItem" v-for="(imgItem, imgIndex) in prizeImgList" :key="imgIndex">
          <img :src="imgItem.topShowImg" alt="">
        </div>
      </div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/340633/18/8430/374/68bea3b9F1e2b9f07/dea157afd2d9aa60.png" alt="" class="rightBtn"/>
    </div>
    <div class="twoDivClass" :style="furnishStyles.blindBoxBg.value">
      <div class="prizeClass">
        <div class="prizeItemClass" v-for="(item, index) in prizeListAll" :key="index">
          <img class="boxPrizes" v-if="item.isSelect" :src="item.lightLogoImg" alt=""/>
          <img class="boxPrizes" v-else :src="item.logoImg" alt=""/>
          <div class="chooseBtn" :style="furnishStyles.drawBtn.value" @click.stop="selectMhClick(item, index)"></div>
        </div>
      </div>
      <div class="numText" :style="furnishStyles.prizeRemainNum.value">
        剩余数量：<span>0</span>（礼品总数）
      </div>
    </div>
    <div class="winners" :style="furnishStyles.winnerBg.value">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <VueDanmaku v-if="activityGiftRecords.length !== 0" ref="danmaku" v-model:danmus="activityGiftRecords" useSlot loop :channels="2" :speeds="100" class="danmaku">
            <template v-slot:dm="{ danmu }">
              <div class="winner" :style="furnishStyles.winnersDanMu.value">
                <span>恭喜{{ danmu.nickName }}抽中了{{ danmu.prizeName }}</span>
              </div>
            </template>
          </VueDanmaku>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--    我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
    </VanPopup>
    <!--抽奖记录弹窗-->
    <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
      <DrawRecordPopup @close="showDrawRecord = false" ></DrawRecordPopup>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject, reactive } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import { prizeListAll } from '../ts/type';
import VueDanmaku from 'vue3-danmaku';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);
const total = ref(0);

const shopName = ref('xxx自营旗舰店');
const shopLogo = ref('http://img30.360buyimg.com/popshop/jfs/t12040/167/1277706166/88856/87939c85/5a1e9842N3d659b8f.jpg');

const isLoadingFinish = ref(false);
const showDrawRecord = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const showMyPrize = ref(false);

const prizeImgList = ref([]);

const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    prizeName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    prizeName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    prizeName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    prizeName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    prizeName: '20积分',
  },
]);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
// 是否有选中的盲盒
const isSelectBlindBox = ref(false);
const isSelectBlindBoxIndex = ref(-1);
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 选择盲盒
const selectMhClick = (item:any, index:number) => {
  for (let i = 0; i < prizeListAll.length; i++) {
    prizeListAll[i].isSelect = false;
  }
  prizeListAll[index].isSelect = true;
  isSelectBlindBox.value = true;
  isSelectBlindBoxIndex.value = index + 1;
};
// 拆盲盒
const startPlay = () => {
  if (!isSelectBlindBox.value) {
    showToast('请先选中心仪盲盒');
    return;
  }
  for (let i = 0; i < prizeListAll.length; i++) {
    if (prizeListAll[i].position === isSelectBlindBoxIndex.value) {
      if (prizeListAll[i].isDraw) {
        showToast('该盲盒已经开过了哦');
        return;
      }
    }
  }
  // 假设后端返回的中奖索引是0
  award.value = {
    prizeType: 0,
    prizeName: '谢谢参与',
    showImg: '',
    result: {
      result: {
        planDesc: '',
      },
    },
    activityPrizeId: '',
    userPrizeId: '',
  };
  showAward.value = true;

  for (let i = 0; i < prizeListAll.length; i++) {
    if (prizeListAll[i].position === isSelectBlindBoxIndex.value) {
      prizeListAll[i].isDraw = true;
    }
  }

};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: {
        result: {
          planDesc: '',
        },
      },
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    console.log('data-C',data)
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    // drawPoints.value = data.points;
    prizeImgList.value = data.mainPrizeList;
    prizeImgList.value.push({
      ...data.bottomPrizeList[0]
    })
    ruleTest.value = data.rules;
    shopName.value = data.shopName;
    shopLogo.value = data.logoUrl;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'shop') {
    shopName.value = data;
  } else if (type === 'shopLogo') {
    shopLogo.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeImgList.value = data.mainPrizeList;
    prizeImgList.value.push({
      ...data.bottomPrizeList[0]
    })
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
    shopLogo.value = activityData.logoUrl;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  if (activityGiftRecords.length > 4) {
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 5,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding: 3.6rem 0 0;
}
  .header-content {
    position: absolute;
    top: 1rem;
    right: 0;
    width: 1.13rem;
  }
  .header-btn {
    background-repeat: no-repeat;
    background-size: 100%;
    width: 1.13rem;
    height: 0.51rem;
    font-size: 0.2rem;
    line-height: 0.4rem;
    padding: 0.04rem 0 0.04rem 0.1rem;
    margin-bottom: 0.1rem;
    text-align: center;
    cursor: pointer;
  }

  .prizeBox{
    width: 5.2rem;
    height: 1.34rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    .leftBtn{
      width: 0.16rem;
      height: 0.29rem;
    }
    .prizeListClass{
      flex: 1;
      display: flex;
      overflow-x: auto;
      overflow-y: hidden;
      margin: 0 0.1rem;
      align-items: center;
      &::-webkit-scrollbar {
        display: none;
      }
      .prizeItem{
        width: 1.5rem;
        height: 1.5rem;
        background: url("https://img10.360buyimg.com/imgzone/jfs/t1/341681/16/1024/4936/68bea3b9F449fae12/c1b845ce84578a0e.png") no-repeat;
        background-size: 100%;
        display: flex;
        align-items: center;
        margin: 0 0.05rem;
        img{
          width: 0.8rem;
          height: 0.8rem;
          margin: 0 auto;
        }
      }
    }
    .rightBtn{
      width: 0.16rem;
      height: 0.29rem;
    }
  }

.twoDivClass{
  width: 7.5rem;
  height: 11rem;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-top: -0.6rem;
  position: relative;
  .numText {
    position: absolute;
    font-size: 0.2rem;
    bottom: 0.45rem;
  }
  .prizeClass{
    width: 5.3rem;
    height: 5rem;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin-top: 2.9rem;
    .prizeItemClass{
      flex: 1.73rem;
      margin: 0 auto 0.23rem;
      .selectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/237000/27/5276/2892/65697800F1cd69330/3d3d413da692bff5.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.5rem;
        height: 0.49rem;
      }
      .noSelectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/224061/3/5097/2766/65697800F1c398617/4b73537eb60825b6.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width:1.5rem;
        height: 0.49rem;
      }
      .lightBoxPrizes{
        width: 1.33rem;
        height: 1.68rem;
        margin: 0 auto;
      }
        .boxPrizes {
          width: 1.25rem;
          height: 1.6rem;
          margin: 0 auto;
        }
        .chooseBtn{
          background-size: 100%;
          background-repeat: no-repeat;
          width: 1.01rem;
          height: 0.36rem;
          margin: -0.1rem auto 0;
          z-index: 10;
          position: relative;
        }
    }
    .bottomBtnDivAll{
      position: absolute;
      bottom: -1.3rem;
      display: flex;
      width: 100%;
      -webkit-box-pack: center;
      justify-content: center;
      .change-blind-box{
        width: 1.2rem;
        height: 0.75rem;
        left: 0.02rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .open-blind-box{
        width: 2.92rem;
        height: 0.83rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .my-prize{
        width: 1.27rem;
        height: 0.75rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }

}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  height: 3.26rem;
  margin: 0 auto;

  .winners-content {
    width: 7.5rem;
    height: 3.26rem;
    margin: 0 auto;
    overflow: hidden;
    padding-top: 0.8rem;
  }
  .danmaku {
    width: 7.5rem;
    height: 3rem;
    margin: 0 auto;
  }
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.winner {
  height: 0.83rem;
  width: 4.55rem;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-left: 0.9rem;
  padding-right: 0.4rem;
  color: #f5e4bd;
  font-size: 0.2rem;
  text-align: center;
  line-height: 0.92rem;
  margin: 0.1rem 0 0 0.5rem;
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none !important;
}
</style>
